package com.ruoyi.web.controller.api;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.system.domain.LotteryActivity;
import com.ruoyi.system.domain.LotteryRecord;
import com.ruoyi.system.domain.Merchant;
import com.ruoyi.system.domain.MerchantTable;
import com.ruoyi.system.domain.dto.DrawLotteryRequest;
import com.ruoyi.system.service.ILotteryActivityService;
import com.ruoyi.system.service.ILotteryRecordService;
import com.ruoyi.system.service.IMerchantService;
import com.ruoyi.system.service.IMerchantTableService;

/**
 * 抽奖API接口 - 小程序端调用
 *
 * <AUTHOR>
 */
@Anonymous
@RestController
@RequestMapping("/api/lottery")
public class ApiLotteryController extends BaseController
{
    @Autowired
    private IMerchantService merchantService;

    @Autowired
    private IMerchantTableService merchantTableService;

    @Autowired
    private ILotteryActivityService lotteryActivityService;

    @Autowired
    private ILotteryRecordService lotteryRecordService;

    /**
     * 根据商家编码获取当前有效的抽奖活动
     */
    @GetMapping("/activity/{merchantCode}")
    public AjaxResult getCurrentActivity(@PathVariable String merchantCode)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        LotteryActivity activity = lotteryActivityService.selectCurrentValidActivity(merchant.getMerchantId());
        if (activity == null)
        {
            return error("暂无进行中的抽奖活动");
        }
        
        return success(activity);
    }

    /**
     * 根据商家编码获取所有有效的抽奖活动
     */
    @GetMapping("/activities/{merchantCode}")
    public AjaxResult getValidActivities(@PathVariable String merchantCode)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        List<LotteryActivity> activities = lotteryActivityService.selectValidLotteryActivities(merchant.getMerchantId());
        return success(activities);
    }

    /**
     * 检查用户是否可以参与抽奖
     */
    @GetMapping("/check/{activityId}/{userOpenid}")
    public AjaxResult checkParticipate(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        boolean canParticipate = lotteryActivityService.canUserParticipate(activityId, userOpenid);
        if (canParticipate) {
            int remaining = lotteryActivityService.getUserRemainingDraws(activityId, userOpenid);
            return AjaxResult.success("可以参与抽奖", remaining);
        } else {
            return error("您今日的抽奖次数已用完或活动已结束");
        }
    }

    /**
     * 获取用户剩余抽奖次数
     */
    @GetMapping("/remaining/{activityId}/{userOpenid}")
    public AjaxResult getRemainingDraws(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        int remaining = lotteryActivityService.getUserRemainingDraws(activityId, userOpenid);
        return success(remaining);
    }

    /**
     * 获取用户抽奖次数详情
     */
    @GetMapping("/draws-info/{activityId}/{userOpenid}")
    public AjaxResult getDrawsInfo(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        try
        {
            // 检查活动是否存在
            LotteryActivity activity = lotteryActivityService.selectLotteryActivityById(activityId);
            if (activity == null)
            {
                return error("活动不存在");
            }

            // 获取用户抽奖统计信息
            int todayDraws = lotteryRecordService.countUserTodayDraws(activityId, userOpenid);
            int totalDraws = lotteryRecordService.countUserTotalDraws(activityId, userOpenid);
            int remainingDraws = lotteryActivityService.getUserRemainingDraws(activityId, userOpenid);

            Map<String, Object> result = new HashMap<>();
            result.put("dailyLimit", activity.getDailyLimit());
            result.put("totalLimit", activity.getTotalLimit());
            result.put("todayDraws", todayDraws);
            result.put("totalDraws", totalDraws);
            result.put("remainingDraws", remainingDraws);

            // 计算今日剩余次数
            int dailyRemaining = 0;
            if (activity.getDailyLimit() != null && activity.getDailyLimit() > 0)
            {
                dailyRemaining = Math.max(0, activity.getDailyLimit() - todayDraws);
            }
            result.put("dailyRemaining", dailyRemaining);

            // 计算总剩余次数
            int totalRemaining = 0;
            if (activity.getTotalLimit() != null && activity.getTotalLimit() > 0)
            {
                totalRemaining = Math.max(0, activity.getTotalLimit() - totalDraws);
            }
            result.put("totalRemaining", totalRemaining);

            return success(result);
        }
        catch (Exception e)
        {
            return error(e.getMessage());
        }
    }

    /**
     * 执行抽奖
     */
    @PostMapping("/draw")
    public AjaxResult performDraw(@Valid @RequestBody DrawLotteryRequest request,
                                HttpServletRequest httpRequest)
    {
        try
        {
            // 验证桌台信息（如果提供了桌台ID）
            if (request.getTableId() != null)
            {
                MerchantTable table = merchantTableService.selectMerchantTableById(request.getTableId());
                if (table == null || !"0".equals(table.getStatus()))
                {
                    return error("桌台信息无效");
                }
            }

            String drawIp = IpUtils.getIpAddr(httpRequest);
            LotteryRecord record = lotteryRecordService.performDraw(request.getActivityId(), request.getUserOpenid(),
                request.getUserNickname(), request.getUserAvatar(), request.getTableId(), drawIp);

            return AjaxResult.success("抽奖成功", record);
        }
        catch (Exception e)
        {
            return error(e.getMessage());
        }
    }

    /**
     * 获取用户抽奖记录
     */
    @GetMapping("/records/{userOpenid}")
    public AjaxResult getUserRecords(@PathVariable String userOpenid)
    {
        List<LotteryRecord> records = lotteryRecordService.selectLotteryRecordsByUserOpenid(userOpenid);
        return success(records);
    }

    /**
     * 获取用户在指定活动的抽奖记录
     */
    @GetMapping("/records/{activityId}/{userOpenid}")
    public AjaxResult getUserActivityRecords(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        List<LotteryRecord> records = lotteryRecordService.selectUserActivityRecords(activityId, userOpenid);
        return success(records);
    }

    /**
     * 获取用户中奖记录
     */
    @GetMapping("/winning/{userOpenid}")
    public AjaxResult getUserWinningRecords(@PathVariable String userOpenid)
    {
        LotteryRecord queryRecord = new LotteryRecord();
        queryRecord.setUserOpenid(userOpenid);
        List<LotteryRecord> records = lotteryRecordService.selectWinningRecords(queryRecord);
        return success(records);
    }

    /**
     * 获取用户未领取的中奖记录
     */
    @GetMapping("/unclaimed/{userOpenid}")
    public AjaxResult getUserUnclaimedRecords(@PathVariable String userOpenid)
    {
        LotteryRecord queryRecord = new LotteryRecord();
        queryRecord.setUserOpenid(userOpenid);
        List<LotteryRecord> records = lotteryRecordService.selectUnclaimedWinningRecords(queryRecord);
        return success(records);
    }

    /**
     * 查询用户今日抽奖次数
     */
    @GetMapping("/count/today/{activityId}/{userOpenid}")
    public AjaxResult getTodayDrawCount(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        int count = lotteryRecordService.countUserTodayDraws(activityId, userOpenid);
        return success(count);
    }

    /**
     * 查询用户总抽奖次数
     */
    @GetMapping("/count/total/{activityId}/{userOpenid}")
    public AjaxResult getTotalDrawCount(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        int count = lotteryRecordService.countUserTotalDraws(activityId, userOpenid);
        return success(count);
    }

    /**
     * 根据商家编码和桌台号获取抽奖信息
     */
    @GetMapping("/info/{merchantCode}/{tableNumber}")
    public AjaxResult getLotteryInfo(@PathVariable String merchantCode, @PathVariable String tableNumber)
    {
        // 检查商家是否有效
        if (!merchantService.isMerchantValid(merchantCode))
        {
            return error("商家已过期或状态异常，请联系管理员");
        }
        
        Merchant merchant = merchantService.selectMerchantByCode(merchantCode);
        if (merchant == null)
        {
            return error("商家不存在");
        }
        
        // 获取桌台信息
        MerchantTable table = merchantTableService.selectMerchantTableByMerchantIdAndNumber(
            merchant.getMerchantId(), tableNumber);
        if (table == null || !"0".equals(table.getStatus()))
        {
            return error("桌台信息无效");
        }
        
        // 获取当前有效的抽奖活动
        LotteryActivity activity = lotteryActivityService.selectCurrentValidActivity(merchant.getMerchantId());
        if (activity == null)
        {
            return error("暂无进行中的抽奖活动");
        }
        
        // 组装返回数据
        Map<String, Object> result = new HashMap<>();
        result.put("merchant", merchant);
        result.put("table", table);
        result.put("activity", activity);
        
        return success(result);
    }

    /**
     * 获取用户在指定活动中的抽奖状态
     * 如果用户已经抽过奖，返回抽奖结果；如果没有抽过奖，返回可以抽奖的状态
     */
    @GetMapping("/status/{activityId}/{userOpenid}")
    public AjaxResult getUserLotteryStatus(@PathVariable Long activityId, @PathVariable String userOpenid)
    {
        try
        {
            // 检查活动是否存在
            LotteryActivity activity = lotteryActivityService.selectLotteryActivityById(activityId);
            if (activity == null)
            {
                return error("活动不存在");
            }

            // 获取用户在该活动中的抽奖记录
            List<LotteryRecord> records = lotteryRecordService.selectUserActivityRecords(activityId, userOpenid);

            Map<String, Object> result = new HashMap<>();

            if (records.isEmpty())
            {
                // 用户还没有抽过奖
                result.put("hasDrawn", false);
                result.put("canDraw", lotteryActivityService.canUserParticipate(activityId, userOpenid));
                result.put("remainingDraws", lotteryActivityService.getUserRemainingDraws(activityId, userOpenid));
            }
            else
            {
                // 用户已经抽过奖，返回抽奖结果
                LotteryRecord record = records.get(0); // 取第一条记录（因为每个用户只能抽一次）
                result.put("hasDrawn", true);
                result.put("canDraw", false);
                result.put("remainingDraws", 0);
                result.put("lotteryRecord", record);
            }

            return success(result);
        }
        catch (Exception e)
        {
            return error(e.getMessage());
        }
    }
}

# 扫码点餐抽奖系统 - UniApp 小程序

## 项目简介

这是一个基于 UniApp 开发的扫码点餐抽奖系统小程序，用户可以通过扫描桌台二维码参与抽奖活动，并跳转到美团小程序进行点餐。

## 功能特性

- 🎯 **扫码识别** - 扫描桌台二维码获取商家和活动信息
- 🎰 **九宫格抽奖** - 精美的九宫格动画和抽奖逻辑（已更新）
- 📱 **美团跳转** - 无缝跳转到美团小程序点餐
- 📋 **记录管理** - 用户抽奖记录和奖品管理
- 🎁 **奖品领取** - 完整的奖品领取流程
- 🎨 **精美 UI** - 渐变色彩和动画效果

## 项目结构

```
prize-draw-order-uniapp/
├── pages/                  # 页面文件
│   ├── index/             # 首页 - 扫码入口
│   ├── lottery/           # 抽奖页面 - 九宫格抽奖
│   ├── result/            # 结果页面 - 抽奖结果展示
│   ├── records/           # 记录页面 - 用户抽奖记录
│   └── claim/             # 领取页面 - 奖品领取
├── static/                # 静态资源
├── utils/                 # 工具类
├── App.vue               # 应用入口组件
├── main.js               # 主入口文件
├── pages.json            # 页面路由配置
├── manifest.json         # 应用配置文件
├── uni.scss              # 全局样式
└── package.json          # 项目依赖配置
```

## 开发环境

### 推荐方式：HBuilderX

1. **下载安装 HBuilderX**

   - 访问：https://www.dcloud.io/hbuilderx.html
   - 下载并安装 HBuilderX 标准版（免费）

2. **导入项目**

   - 打开 HBuilderX
   - 文件 → 导入 → 从本地目录导入
   - 选择项目根目录

3. **运行项目**
   - 右键项目根目录
   - 运行 → 运行到小程序模拟器 → 微信开发者工具

### 命令行方式

```bash
# 安装依赖
npm install

# 开发微信小程序
npm run dev:mp-weixin

# 构建微信小程序
npm run build:mp-weixin
```

## 配置说明

### 1. 小程序配置

在 `manifest.json` 中配置小程序 AppID：

```json
{
  "mp-weixin": {
    "appid": "your-mini-program-appid"
  }
}
```

### 2. API 接口配置

在 `utils/api.js` 中配置后端 API 地址。

## 九宫格抽奖功能

### 功能特点

- **3x3 九宫格布局**：中间为抽奖按钮，周围 8 个位置展示奖品
- **流畅动画效果**：跑马灯式的高亮动画，视觉效果佳
- **智能奖品配置**：自动适配后台设置的奖项，不足 8 个自动填充
- **剩余次数显示**：中心按钮实时显示剩余抽奖次数
- **响应式设计**：适配不同屏幕尺寸和设备

### 实现文件

- `pages/lottery/lottery.vue` - 主抽奖页面（已更新为九宫格）
- `pages/lottery/demo.vue` - 九宫格抽奖演示页面
- `lottery-preview.html` - 浏览器预览页面（可直接打开查看效果）

### 动画逻辑

1. 点击中心按钮开始抽奖
2. 九宫格按顺序高亮显示（跑马灯效果）
3. 转 3 圈后逐渐减速
4. 停在中奖奖品位置
5. 弹窗显示中奖结果

## 页面说明

- **首页 (index)** - 扫码入口、商家信息、活动介绍
- **抽奖页面 (lottery)** - 九宫格抽奖、奖品展示、抽奖规则
- **结果页面 (result)** - 中奖动画、奖品信息、领取说明
- **记录页面 (records)** - 抽奖历史、奖品状态、筛选功能
- **领取页面 (claim)** - 奖品详情、领取表单、确认领取

## 技术栈

- **框架**：UniApp
- **语言**：Vue.js 2.x + JavaScript
- **样式**：Sass/SCSS
- **平台**：微信小程序 + H5

## 支持平台

- ✅ 微信小程序
- ✅ H5
- ⚠️ 其他平台（需要适配）

---

**推荐使用 HBuilderX 进行开发！**

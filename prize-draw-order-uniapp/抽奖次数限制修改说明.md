# 抽奖次数限制修改说明

## 修改概述

本次修改实现了基于后端接口的抽奖次数限制功能，支持单日抽奖次数和总抽奖次数的限制。

## 主要修改内容

### 1. 后端修改

#### 1.1 LotteryActivityServiceImpl.java
**文件路径**: `prize-draw-order-ruoyi/ruoyi-system/src/main/java/com/ruoyi/system/service/impl/LotteryActivityServiceImpl.java`

**修改内容**:
- 恢复了`canUserParticipate()`方法中的每日限制和总限制检查逻辑
- 重新实现了`getUserRemainingDraws()`方法，根据活动配置的`dailyLimit`和`totalLimit`计算剩余次数

**核心逻辑**:
```java
// 检查每日限制
if (activity.getDailyLimit() != null && activity.getDailyLimit() > 0) {
    int todayDraws = lotteryRecordMapper.countUserTodayDraws(activityId, userOpenid);
    if (todayDraws >= activity.getDailyLimit()) {
        return false;
    }
}

// 检查总限制
if (activity.getTotalLimit() != null && activity.getTotalLimit() > 0) {
    int totalDraws = lotteryRecordMapper.countUserTotalDraws(activityId, userOpenid);
    if (totalDraws >= activity.getTotalLimit()) {
        return false;
    }
}
```

#### 1.2 ApiLotteryController.java
**文件路径**: `prize-draw-order-ruoyi/ruoyi-admin/src/main/java/com/ruoyi/web/controller/api/ApiLotteryController.java`

**新增接口**:
- 添加了`/api/lottery/draws-info/{activityId}/{userOpenid}`接口
- 返回用户抽奖次数的详细信息，包括：
  - `dailyLimit`: 每日限制次数
  - `totalLimit`: 总限制次数
  - `todayDraws`: 今日已抽奖次数
  - `totalDraws`: 总已抽奖次数
  - `remainingDraws`: 剩余抽奖次数
  - `dailyRemaining`: 今日剩余次数
  - `totalRemaining`: 总剩余次数

### 2. 前端修改

#### 2.1 API接口 (utils/api.js)
**新增方法**:
```javascript
// 获取用户抽奖次数详情
getDrawsInfo(activityId, userOpenid) {
  return request({
    url: `/api/lottery/draws-info/${activityId}/${userOpenid}`,
  });
}
```

#### 2.2 抽奖页面 (pages/lottery/lottery.vue)
**主要修改**:
1. 添加了`drawsInfo`数据字段存储抽奖次数详情
2. 新增了`loadDrawsInfo()`方法获取抽奖次数详情
3. 添加了抽奖次数信息显示区域
4. 新增了`getRemainingText()`方法动态生成剩余次数显示文本
5. 优化了抽奖次数用完时的提示信息

**新增UI组件**:
- 抽奖次数信息区域，显示今日限制、总限制和剩余次数
- 动态的剩余次数显示文本

## 功能特性

### 1. 灵活的次数限制
- 支持仅设置每日限制
- 支持仅设置总限制
- 支持同时设置每日限制和总限制
- 支持无限制模式

### 2. 详细的次数信息显示
- 显示今日已抽奖次数/每日限制
- 显示总已抽奖次数/总限制
- 显示对应的剩余次数
- 动态更新抽奖按钮文本

### 3. 智能的限制逻辑
- 当同时有每日限制和总限制时，取较小的剩余次数
- 实时更新抽奖状态
- 提供详细的错误提示信息

## 测试方法

### 1. 后端测试
1. 启动后端服务
2. 在管理后台创建抽奖活动，设置不同的`dailyLimit`和`totalLimit`值
3. 使用API测试工具调用相关接口验证功能

### 2. 前端测试
1. 启动uniapp项目
2. 访问抽奖页面
3. 验证抽奖次数信息显示是否正确
4. 进行抽奖操作，验证次数限制是否生效

### 3. 测试场景
- **场景1**: 设置每日限制为2次，验证用户每天最多只能抽奖2次
- **场景2**: 设置总限制为5次，验证用户总共最多只能抽奖5次
- **场景3**: 同时设置每日限制2次和总限制3次，验证限制逻辑
- **场景4**: 不设置任何限制，验证无限制模式

## 数据库配置

抽奖活动表(`lottery_activity`)中的相关字段：
- `daily_limit`: 每日抽奖次数限制（NULL或0表示无限制）
- `total_limit`: 总抽奖次数限制（NULL或0表示无限制）

## 注意事项

1. 每日限制基于用户的抽奖时间(`draw_time`)字段进行统计
2. 总限制基于用户在指定活动中的总抽奖次数进行统计
3. 前端会实时从后端获取最新的抽奖次数信息
4. 抽奖成功后会自动刷新抽奖次数信息
